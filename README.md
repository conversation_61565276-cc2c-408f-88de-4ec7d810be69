# SmartClause

> 一个专为合同审核业务设计的前端组件库，深度集成 OnlyOffice 文档编辑器，提供智能化的风险项管理和备注系统。基于 TypeScript + Alpine.js 构建，支持现代浏览器和 IE11。

## 🎯 项目定位

SmartClause 是一个企业级的合同审核前端解决方案，旨在提升法务团队的工作效率。通过与 OnlyOffice 文档编辑器的深度集成，实现了：

- **📄 文档编辑器无缝集成**：基于 OnlyOffice API 的深度定制
- **🔍 智能风险识别**：自动化的合同风险项检测和展示
- **👥 协作审核流程**：多用户实时协作的合同审核工作流
- **⚡ 高性能渲染**：支持大量风险项的流畅展示和操作
- **🎨 响应式界面**：基于 Alpine.js 的轻量级响应式用户界面
- **🛡️ 类型安全**：完整的 TypeScript 类型定义和错误处理

## 🏗️ 技术架构

```
SmartClause 架构图
┌─────────────────────────────────────────────────────────────┐
│                    SmartClause 组件库                        │
├─────────────────────┬───────────────────────────────────────┤
│   OnlyOfficeManager │          RiskItemsManager             │
│   ┌───────────────┐ │   ┌─────────────────────────────────┐ │
│   │ 编辑器管理     │ │   │ Alpine.js 响应式界面             │ │
│   │ 备注系统       │ │   │ 风险项筛选和分类                 │ │
│   │ 事件处理       │ │   │ 实时数据同步                     │ │
│   │ 跳转定位       │ │   │ 用户交互回调                     │ │
│   └───────────────┘ │   └─────────────────────────────────┘ │
└─────────────────────┴───────────────────────────────────────┤
│                     公共基础设施                              │
│  ┌──────────────┬──────────────┬─────────────────────────┐  │
│  │ TypeScript   │ 事件通信系统  │ 工具函数库               │  │
│  │ 类型系统     │ AppMessage   │ JSON处理/防抖/节流       │  │
│  └──────────────┴──────────────┴─────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                    ┌─────────┴─────────┐
                    │    OnlyOffice     │
                    │   Document API    │
                    └───────────────────┘
```

## 📋 目录

- [SmartClause](#smartclause)
  - [📋 目录](#-目录)
  - [✨ 功能特性](#-功能特性)
    - [OnlyOfficeManager 核心功能](#onlyofficemanager-核心功能)
    - [RiskItemsManager 核心功能](#riskitemsmanager-核心功能)
    - [高级特性](#高级特性)
  - [🚀 安装使用](#-安装使用)
    - [前置要求](#前置要求)
    - [基本使用](#基本使用)
  - [组件说明](#组件说明)
    - [OnlyOfficeManager](#onlyofficemanager)
    - [RiskItemsManager](#riskitemsmanager)
  - [⚙️ 配置说明](#️-配置说明)
    - [OnlyOfficeManagerConfig](#onlyofficemanagerconfig)
    - [RiskItemsCallbacks](#riskitemscallbacks)
  - [📚 API 文档](#-api-文档)
    - [OnlyOfficeManager 核心方法](#onlyofficemanager-核心方法)
    - [RiskItemsManager 核心方法](#riskitemsmanager-核心方法)
  - [🔔 事件系统](#-事件系统)
    - [OnlyOfficeManager 事件](#onlyofficemanager-事件)
    - [RiskItemsManager 事件](#riskitemsmanager-事件)
  - [💡 使用示例](#-使用示例)
    - [完整的合同审核流程](#完整的合同审核流程)
  - [🛠️ 构建与部署](#️-构建与部署)
    - [开发环境](#开发环境)
    - [生产环境构建](#生产环境构建)
    - [开发环境构建](#开发环境构建)
    - [本地预览](#本地预览)
  - [⚠️ 注意事项](#️-注意事项)
    - [环境要求](#环境要求)
    - [性能优化](#性能优化)
    - [安全考虑](#安全考虑)
    - [浏览器兼容性](#浏览器兼容性)
  - [👨‍💻 贡献指南](#-贡献指南)
  - [📄 许可证](#-许可证)
  - [📝 更新日志](#-更新日志)
    - [v2.1.0](#v210)
    - [v2.0.0](#v200)
    - [v1.0.0](#v100)

## ✨ 功能特性

### OnlyOfficeManager 核心功能
- 🏗️ **编辑器管理** - 初始化、销毁和配置 OnlyOffice 编辑器
- 📝 **备注系统** - 完整的备注增删改查功能
- 🎯 **智能跳转** - 支持按对象ID循环跳转相关备注
- 🎨 **样式管理** - 自动更新备注显示样式和颜色
- 🔄 **事件驱动** - 完整的事件通知系统
- 📊 **数据解析** - 智能解析和管理备注数据

### RiskItemsManager 核心功能
- 📋 **风险项管理** - 基于 Alpine.js 的响应式风险项管理界面
- 🔍 **筛选功能** - 支持按风险等级、角色、当事人类型和文本搜索筛选
- 🏷️ **分类展示** - 按风险项类型（缺失条款和风险提示）分区域渲染
- 📌 **快速定位** - 支持滚动到指定风险项并高亮显示
- 📝 **备注管理** - 支持展开/折叠、保存风险项备注内容
- 🔄 **事件通知** - 完整的自定义事件通知机制

### 高级特性
- ⚡ **节流控制** - 防止频繁操作导致的性能问题
- 🔒 **状态管理** - 完善的初始化状态检查
- 📱 **响应式设计** - 支持不同设备和屏幕尺寸
- 🛡️ **错误处理** - 完整的异常捕获和错误提示
- 🏷️ **类型安全** - 完整的 TypeScript 类型定义
- 🔙 **向后兼容** - 支持 IE11 和较旧的浏览器版本

## 🚀 快速开始

### 前置要求

- **Node.js** >= 16.0.0
- **pnpm** 或 **npm** 包管理器
- **OnlyOffice Document Server** 服务器环境
- **Alpine.js** >= 3.x (CDN 或本地引入)

```bash
# 克隆项目
git clone <your-repo-url>
cd smart-clause

# 安装依赖 (推荐使用 pnpm)
pnpm install
# 或使用 npm
npm install
```

### 环境配置

项目使用 Vite 构建工具，支持开发和生产环境：

```bash
# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build:prod

# 构建开发版本（包含源码映射）
pnpm build:dev

# 预览构建结果
pnpm preview
```

### 基本使用

#### 1. HTML 页面结构

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartClause 合同审核</title>
    <!-- 引入 Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <!-- 引入 OnlyOffice API -->
    <script src="https://your-onlyoffice-server.com/OfficeWeb/apps/api/documents/api.js"></script>
    <!-- 引入构建后的脚本 -->
    <script type="module" src="./dist/static/js/main.js"></script>
</head>
<body>
    <div style="display: flex; height: 100vh;">
        <!-- OnlyOffice 编辑器容器 -->
        <div id="onlyoffice-container" style="flex: 2;"></div>
        <!-- 风险项管理容器 -->
        <div id="risk-items-container" style="flex: 1;" x-data="riskItemsData">
            <!-- 风险项界面将在这里渲染 -->
        </div>
    </div>
</body>
</html>
```

#### 2. JavaScript 初始化

```typescript
import { OnlyOfficeManager } from './ts/OnlyOfficeManager';
import { RiskItemsManager } from './ts/RiskItemsManager';
import type { OnlyOfficeManagerConfig, RiskItemsCallbacks } from './ts/types/onlyoffice';

// 配置参数
const config: OnlyOfficeManagerConfig = {
  containerId: 'onlyoffice-container',
  rightMenuId: 'risk-items-container',
  config: {
    documentType: 'word',
    document: {
      fileType: 'docx',
      key: 'document-key',
      title: '合同文档.docx',
      url: 'https://example.com/document.docx',
      permissions: {
        comment: true,
        download: true,
        edit: true,
        print: true,
        review: true
      }
    },
    editorConfig: {
      mode: 'edit',
      lang: 'zh-CN',
      user: {
        id: 'user-123',
        name: '张三'
      },
      customization: {
        compactToolbar: true,
        feedback: false,
        forceSave: true,
        showReviewChanges: true,
        autosave: true
      }
    }
  },
  isShowComment: true,
  isDevelopment: false,
  subscribeRoles: 'admin,reviewer',
  riskLevels: [
    { objectId: "1", name: "严重" },
    { objectId: "2", name: "一般" },
    { objectId: "3", name: "建议" }
  ],
  relationUrl: 'https://api.example.com/relations',
  showSign: true,
  showKnowledgeSource: true,
  showEditRadio: true
};

// 创建回调函数
const callbacks: RiskItemsCallbacks = {
  onItemJump: async (item) => {
    // 跳转到文档中的备注位置
    return onlyOfficeManager.scrollToCommentByObjectId(item.objectId);
  },
  onItemDelete: async (item) => {
    // 删除文档中的备注
    return await onlyOfficeManager.deleteCommentByCommentId([item.commentId]);
  },
  onTextInsert: async (hint, item) => {
    // 在文档中插入建议文本
    return await onlyOfficeManager.applySuggestion(hint);
  },
  onSaveItem: async (item) => {
    // 保存风险项到后端
    try {
      const response = await fetch('/api/risk-items', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(item)
      });
      return response.ok;
    } catch (error) {
      console.error('保存失败:', error);
      return false;
    }
  }
};

// 创建管理器实例
const onlyOfficeManager = new OnlyOfficeManager(config);
const riskItemsManager = new RiskItemsManager(config, callbacks);

// 初始化编辑器和风险项管理器
onlyOfficeManager.init();
riskItemsManager.init();

// 监听文档加载完成事件
document.addEventListener('onlyoffice-comments-loaded', (event) => {
  const { docData } = event.detail;
  riskItemsManager.setData(docData);
  console.log(`已加载 ${docData.length} 个风险项`);
});

// 监听备注点击事件，实现双向跳转
document.addEventListener('onlyoffice-comment-clicked', (event) => {
  const { firstCommentObjectId } = event.detail;
  riskItemsManager.scrollToItemByObjectId(firstCommentObjectId);
});
```

## 组件说明

### OnlyOfficeManager

OnlyOfficeManager 是一个用于管理 OnlyOffice 文档编辑器的 TypeScript 类，专为合同审核前台展示功能设计。它提供了完整的备注管理、跳转、样式设置等功能。

### RiskItemsManager

RiskItemsManager 是一个基于 Alpine.js 构建的风险项管理组件，用于在文档右侧展示和管理风险项。它支持按类型分类展示、筛选、排序、跳转等功能，并提供了完整的事件通知机制。

## ⚙️ 配置说明

### OnlyOfficeManagerConfig

| 参数 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| `containerId` | string | ✅ | - | 编辑器容器 DOM 元素 ID |
| `rightMenuId` | string | ✅ | - | 风险项容器 DOM 元素 ID |
| `config` | OnlyOfficeConfig | ✅ | - | OnlyOffice 原生配置对象 |
| `isShowComment` | boolean | ✅ | true | 是否显示备注功能 |
| `isDevelopment` | boolean | ✅ | false | 是否为开发环境 |
| `subscribeRoles` | string | ✅ | - | 订阅角色列表（逗号分隔） |
| `riskLevels` | `{objectId: string, name: string}[]` | ✅ | - | 风险等级配置数组 |
| `relationUrl` | string | ✅ | - | 关联数据 API 地址 |
| `showSign` | boolean | ✅ | true | 是否显示智合同标题 |
| `showKnowledgeSource` | boolean | ✅ | true | 是否显示知识来源 |
| `showEditRadio` | boolean | ✅ | true | 是否显示确认/忽略单选按钮 |

**riskLevels 配置示例：**
```typescript
riskLevels: [
  { objectId: "1", name: "严重" },
  { objectId: "2", name: "一般" },
  { objectId: "3", name: "建议" }
]
```

**OnlyOfficeConfig 核心配置：**
```typescript
config: {
  documentType: 'word' | 'cell' | 'slide',
  document: {
    fileType: string,      // 文件类型，如 'docx'
    key: string,           // 文档唯一标识
    title: string,         // 文档标题
    url: string,           // 文档下载地址
    permissions: {
      comment: boolean,    // 是否允许评论
      download: boolean,   // 是否允许下载
      edit: boolean,       // 是否允许编辑
      print: boolean,      // 是否允许打印
      review: boolean      // 是否允许审阅
    }
  },
  editorConfig: {
    mode: 'view' | 'edit' | 'review' | 'comment' | 'fillForms',
    lang?: string,         // 界面语言，如 'zh-CN'
    user?: {
      id: string,          // 用户ID
      name: string         // 用户名
    },
    customization?: {
      compactToolbar?: boolean,     // 紧凑工具栏
      feedback?: boolean,           // 反馈按钮
      forceSave?: boolean,          // 强制保存
      showReviewChanges?: boolean,  // 显示审阅更改
      autosave?: boolean            // 自动保存
    }
  }
}
```

### RiskItemsCallbacks

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `onItemJump` | function | ❌ | 跳转到备注的回调函数 |
| `onItemDelete` | function | ❌ | 删除备注的回调函数 |
| `onTextInsert` | function | ❌ | 插入文本的回调函数 |
| `onSaveItem` | function | ❌ | 保存风险项的回调函数 |
| `onRefresh` | function | ❌ | 刷新数据的回调函数 |

## 📚 API 文档

### OnlyOfficeManager 核心方法

#### `init(): boolean`
初始化 OnlyOffice 编辑器

```typescript
const success = onlyOfficeManager.init();
```

#### `destroy(): void`
销毁编辑器实例

```typescript
onlyOfficeManager.destroy();
```

#### `saveDocument(): void`
保存当前文档

```typescript
onlyOfficeManager.saveDocument();
```

#### `scrollToCommentByObjectId(objectId: string): boolean`
滚动到指定 objectId 的备注

```typescript
onlyOfficeManager.scrollToCommentByObjectId('obj-123');
```

#### `deleteCommentByCommentId(commentIds: string[]): Promise<boolean>`
删除指定 commentId 的备注

```typescript
await onlyOfficeManager.deleteCommentByCommentId(['comment-123']);
```

#### `applySuggestion(text: string): Promise<boolean>`
应用文本插入建议

```typescript
await onlyOfficeManager.applySuggestion('建议插入的文本');
```

### RiskItemsManager 核心方法

#### `init(): boolean`
初始化风险项管理器

```typescript
const success = riskItemsManager.init();
```

#### `setData(data: DocData[]): void`
设置风险项数据

```typescript
riskItemsManager.setData(docData);
```

#### `getData(): DocData[]`
获取当前风险项数据

```typescript
const items = riskItemsManager.getData();
```

#### `addItem(item: DocData): void`
添加单个风险项

```typescript
riskItemsManager.addItem(newItem);
```

#### `removeItem(commentId: string): boolean`
根据 commentId 移除风险项

```typescript
const removed = riskItemsManager.removeItem('comment-123');
```

#### `updateItem(item: DocData): boolean`
更新现有风险项

```typescript
const updated = riskItemsManager.updateItem(modifiedItem);
```

#### `removeItemsByObjectId(objectId: string): number`
根据 objectId 移除所有相关风险项

```typescript
const count = riskItemsManager.removeItemsByObjectId('obj-123');
```

#### `clearData(): void`
清空所有风险项数据

```typescript
riskItemsManager.clearData();
```

#### `scrollToItemByCommentId(commentId: string): boolean`
根据 commentId 滚动到风险项卡片

```typescript
riskItemsManager.scrollToItemByCommentId('comment-123');
```

#### `scrollToItemByObjectId(objectId: string): boolean`
根据 objectId 滚动到风险项卡片

```typescript
riskItemsManager.scrollToItemByObjectId('obj-123');
```

#### `getIsReadOnly(): boolean`
获取当前是否为只读模式

```typescript
const isReadOnly = riskItemsManager.getIsReadOnly();
```

#### `destroy(): void`
销毁风险项管理器实例

```typescript
riskItemsManager.destroy();
```

## 🔔 事件系统

### OnlyOfficeManager 事件

#### `onlyoffice-comments-loaded`
备注加载完成事件

```typescript
document.addEventListener('onlyoffice-comments-loaded', (event) => {
  const { comments, docData } = event.detail;
  console.log(`加载了 ${comments.length} 个备注`);
});
```

#### `onlyoffice-comment-clicked`
备注点击事件

```typescript
document.addEventListener('onlyoffice-comment-clicked', (event) => {
  const { firstCommentObjectId, timestamp } = event.detail;
  console.log(`点击了对象 ${firstCommentObjectId} 的备注`);
});
```

#### `onlyoffice-ready`
编辑器准备就绪事件

```typescript
document.addEventListener('onlyoffice-ready', (event) => {
  console.log('编辑器已初始化并准备就绪');
});
```

### RiskItemsManager 事件

#### `risk-items-data-updated`
风险项数据更新事件

```typescript
document.getElementById('risk-items-container').addEventListener('risk-items-data-updated', (event) => {
  const { data } = event.detail;
  console.log(`风险项数据已更新，共 ${data.length} 项`);
});
```

#### `item-jump`
风险项跳转事件

```typescript
document.addEventListener('item-jump', (event) => {
  const { objectId, item } = event.detail;
  console.log(`跳转到风险项: ${item.commentId}`);
});
```

#### `text-insert`
文本插入事件

```typescript
document.addEventListener('text-insert', (event) => {
  const { hint, item } = event.detail;
  console.log(`插入文本: ${hint}`);
});
```

#### `item-save`
风险项保存事件

```typescript
document.addEventListener('item-save', (event) => {
  const { objectId, type, item } = event.detail;
  console.log(`保存风险项: ${item.commentId}`);
});
```

## 💡 使用示例

### 快速体验

项目提供了完整的示例页面，可以快速体验功能：

```bash
# 启动开发服务器
pnpm dev

# 访问示例页面
# http://localhost:3005/examples/risk-items-example.html
```

示例页面包含：
- 模拟的 OnlyOffice 编辑器界面
- 完整的风险项管理功能
- 筛选、搜索、分类展示
- 事件交互演示

### 完整的合同审核流程

```typescript
class ContractReviewApp {
  private onlyOfficeManager: OnlyOfficeManager;
  private riskItemsManager: RiskItemsManager;

  constructor() {
    // 初始化配置
    const config = {
      containerId: 'contract-editor',
      rightMenuId: 'risk-items-panel',
      config: {
        documentType: 'word',
        document: {
          fileType: 'docx',
          key: 'contract-001',
          title: '合同审核文档.docx',
          url: '/api/documents/contract-001.docx'
        },
        editorConfig: {
          mode: 'edit',
          lang: 'zh-CN'
        }
      },
      isShowComment: true,
      isDevelopment: process.env.NODE_ENV === 'development',
      subscribeRoles: 'legal,manager,reviewer',
      riskLevels: { critical: 1, warning: 2, info: 3 },
      relationUrl: '/api/legal/relations',
      showSign: true,
      showKnowledgeSource: true
    };

    // 初始化回调
    const callbacks = {
      onItemJump: async (item) => {
        return this.onlyOfficeManager.scrollToCommentByObjectId(item.objectId);
      },
      onItemDelete: async (item) => {
        return this.deleteComment(item);
      },
      onTextInsert: async (hint, item) => {
        return this.insertText(hint);
      },
      onSaveItem: async (item) => {
        return this.saveItem(item);
      }
    };

    // 初始化管理器
    this.onlyOfficeManager = new OnlyOfficeManager(config);
    this.riskItemsManager = new RiskItemsManager(config, callbacks);

    this.setupEventListeners();
    this.init();
  }

  private async init() {
    try {
      // 初始化编辑器和风险项管理器
      this.onlyOfficeManager.init();
      this.riskItemsManager.init();
    } catch (error) {
      console.error('初始化失败:', error);
    }
  }

  private setupEventListeners() {
    // 监听备注加载完成事件
    document.addEventListener('onlyoffice-comments-loaded', (event) => {
      const { docData } = event.detail;
      this.riskItemsManager.setData(docData);
    });

    // 监听备注点击事件
    document.addEventListener('onlyoffice-comment-clicked', (event) => {
      const { firstCommentObjectId } = event.detail;
      this.riskItemsManager.scrollToItemByObjectId(firstCommentObjectId);
    });
  }

  // 删除备注
  private async deleteComment(item: any) {
    try {
      await this.onlyOfficeManager.deleteCommentByCommentId([item.commentId]);
      return true;
    } catch (error) {
      console.error('删除备注失败:', error);
      return false;
    }
  }

  // 插入文本
  private async insertText(text: string) {
    try {
      await this.onlyOfficeManager.applySuggestion(text);
      return true;
    } catch (error) {
      console.error('插入文本失败:', error);
      return false;
    }
  }

  // 保存风险项
  private async saveItem(item: any) {
    try {
      // 实现保存风险项的逻辑
      console.log('保存风险项:', item);
      return true;
    } catch (error) {
      console.error('保存风险项失败:', error);
      return false;
    }
  }

  // 保存文档
  public saveDocument() {
    this.onlyOfficeManager.saveDocument();
  }

  // 清理资源
  public destroy() {
    this.onlyOfficeManager.destroy();
    this.riskItemsManager.destroy();
  }
}

// 使用示例
const app = new ContractReviewApp();
```

## 🛠️ 构建与部署

### 项目结构

```
smart-clause/
├── src/
│   ├── ts/
│   │   ├── OnlyOfficeManager.ts    # OnlyOffice 编辑器管理器
│   │   ├── RiskItemsManager.ts     # 风险项管理器
│   │   ├── AppMessage.ts           # 消息通信模块
│   │   ├── utils.ts                # 工具函数
│   │   └── types/
│   │       └── onlyoffice.ts       # TypeScript 类型定义
│   ├── css/
│   │   └── risk-items.css          # 风险项样式
│   ├── main.ts                     # 主入口文件
│   └── vite-env.d.ts              # Vite 环境类型
├── examples/
│   └── risk-items-example.html    # 使用示例
├── dist/                          # 构建输出目录
├── package.json
├── tsconfig.json                  # TypeScript 配置
├── vite.config.ts                 # Vite 构建配置
└── README.md
```

### 开发环境

```bash
# 安装依赖 (推荐使用 pnpm)
pnpm install

# 启动开发服务器 (默认端口 3005)
pnpm dev

# TypeScript 类型检查
npx tsc --noEmit
```

### 生产环境构建

```bash
# 构建生产环境版本
pnpm build:prod

# 构建开发环境版本（包含源码映射）
pnpm build:dev

# 通用构建命令
pnpm build
```

### 本地预览

```bash
# 预览构建后的项目
pnpm preview
```

### 构建输出

构建后的文件结构：
```
dist/
├── index.html
├── static/
│   ├── js/
│   │   └── main.js          # 主要 JavaScript 文件
│   └── css/
│       └── risk-items.css   # 样式文件
└── vite.svg
```

## ⚠️ 注意事项

### 环境要求
- 确保页面已正确引入 OnlyOffice 库和 Alpine.js
- 需要在内网环境中部署 OnlyOffice Document Server
- 支持的文档格式：docx, doc, rtf, txt 等
- 建议使用 HTTPS 协议以确保安全性

### 性能优化
- 风险项数量较多时（>1000），考虑分页加载或虚拟滚动
- 使用内置的节流函数避免频繁操作
- 及时调用 `destroy()` 方法释放内存
- 启用 OnlyOffice 的 `autosave` 功能减少手动保存

### 安全考虑
- 验证用户权限后再执行删除操作
- 敏感文档建议启用水印和权限控制
- 定期保存文档避免数据丢失
- 使用 HTTPS 传输文档内容
- 配置适当的 CORS 策略

### 浏览器兼容性
- **现代浏览器**：Chrome 52+, Firefox 75+, Safari 13+, Edge 80+
- **IE 11**：通过 Vite Legacy 插件支持，包含必要的 polyfills
- **移动端**：支持 iOS Safari 13+, Android Chrome 52+

### 技术栈
- **构建工具**：Vite 6.x
- **语言**：TypeScript 5.8+
- **UI 框架**：Alpine.js 3.14+
- **编辑器**：OnlyOffice Document API
- **兼容性**：@vitejs/plugin-legacy

## 🔧 故障排除

### 常见问题

#### 1. 编辑器初始化失败
**症状：** 编辑器容器为空白，控制台出现错误  
**解决方案：**
```typescript
// 检查 OnlyOffice API 是否正确加载
if (typeof window.DocsAPI === 'undefined') {
  console.error('OnlyOffice API 未加载，请检查脚本引入');
}

// 检查容器元素是否存在
const container = document.getElementById('your-container-id');
if (!container) {
  console.error('容器元素未找到');
}
```

#### 2. 风险项数据不显示
**症状：** 风险项面板为空或显示异常  
**解决方案：**
```typescript
// 确保在编辑器初始化完成后设置数据
document.addEventListener('onlyoffice-ready', () => {
  riskItemsManager.setData(yourRiskData);
});

// 检查数据格式是否正确
console.log('风险项数据格式：', riskItemsManager.getData());
```

#### 3. 事件回调不触发
**症状：** 点击跳转、删除等操作无响应  
**解决方案：**
```typescript
// 确保回调函数返回 Promise 或 boolean
const callbacks = {
  onItemJump: async (item) => {
    try {
      // 执行跳转逻辑
      return true; // 必须返回结果
    } catch (error) {
      console.error('跳转失败:', error);
      return false;
    }
  }
};
```

#### 4. 性能问题
**症状：** 大量风险项时界面卡顿  
**解决方案：**
```typescript
// 启用虚拟滚动或分页加载
const ITEMS_PER_PAGE = 100;
function loadItemsByPage(page: number) {
  const start = page * ITEMS_PER_PAGE;
  const end = start + ITEMS_PER_PAGE;
  return allItems.slice(start, end);
}
```

### 错误代码参考

| 错误代码 | 描述 | 解决方案 |
|---------|------|---------|
| `EDITOR_INIT_FAILED` | 编辑器初始化失败 | 检查 OnlyOffice API 加载和配置 |
| `CONTAINER_NOT_FOUND` | 容器元素未找到 | 检查容器 ID 是否正确 |
| `DATA_FORMAT_ERROR` | 数据格式错误 | 检查风险项数据结构 |
| `CALLBACK_ERROR` | 回调函数执行出错 | 检查回调函数实现和返回值 |
| `PERMISSION_DENIED` | 权限不足 | 检查用户权限配置 |

## 🚀 开发指南

### 开发环境搭建

1. **克隆项目并安装依赖**
   ```bash
   git clone <repository-url>
   cd smart-clause
   pnpm install
   ```

2. **启动开发服务器**
   ```bash
   # 启动开发服务器 (http://localhost:3005)
   pnpm dev

   # 构建开发版本（包含源码映射）
   pnpm build:dev
   ```

3. **代码质量检查**
   ```bash
   # TypeScript 类型检查
   npx tsc --noEmit

   # 查看示例页面
   # 访问 http://localhost:3005/examples/risk-items-example.html
   ```

4. **项目配置文件说明**
   - `vite.config.ts`: Vite 构建配置，包含 Legacy 插件设置
   - `tsconfig.json`: TypeScript 编译配置
   - `package.json`: 项目依赖和脚本配置

### 自定义开发

#### 扩展风险项类型
```typescript
// 扩展 DocData 接口
interface CustomDocData extends DocData {
  priority: 'high' | 'medium' | 'low';
  category: string;
  assignee?: string;
}

// 自定义渲染逻辑
class CustomRiskItemsManager extends RiskItemsManager {
  protected renderCustomFields(item: CustomDocData) {
    // 实现自定义字段渲染
  }
}
```

#### 添加自定义事件
```typescript
// 在 OnlyOfficeManager 中添加自定义事件
class CustomOnlyOfficeManager extends OnlyOfficeManager {
  private emitCustomEvent(eventName: string, data: any) {
    const event = new CustomEvent(eventName, { detail: data });
    document.dispatchEvent(event);
  }
  
  // 在适当的位置触发自定义事件
  public customAction() {
    this.emitCustomEvent('custom-action-completed', { timestamp: Date.now() });
  }
}
```

## 🧪 测试指南

### 单元测试
项目使用 TypeScript 类型检查来确保代码质量。建议在业务代码中添加：

```typescript
// 类型断言测试
function validateRiskItem(item: DocData): boolean {
  return (
    typeof item.commentId === 'string' &&
    typeof item.level === 'number' &&
    item.level >= 1 && item.level <= 3
  );
}
```

### 集成测试
```typescript
// 测试完整的工作流
async function testWorkflow() {
  const manager = new OnlyOfficeManager(config);
  const riskManager = new RiskItemsManager(config, callbacks);
  
  // 初始化
  assert(manager.init(), '编辑器初始化失败');
  assert(riskManager.init(), '风险项管理器初始化失败');
  
  // 数据操作
  riskManager.setData(testData);
  assert(riskManager.getData().length > 0, '数据设置失败');
}
```

## 👨‍💻 贡献指南

欢迎贡献代码和提出问题！请遵循以下步骤：

1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的修改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建一个 Pull Request

## 📦 依赖信息

### 生产依赖
- **alpinejs**: ^3.14.9 - 响应式 UI 框架

### 开发依赖
- **typescript**: ~5.8.3 - TypeScript 编译器
- **vite**: ^6.3.5 - 现代构建工具
- **@vitejs/plugin-legacy**: ^6.0.2 - 旧浏览器兼容性支持
- **@types/alpinejs**: ^3.13.11 - Alpine.js 类型定义
- **@rollup/plugin-inject**: ^5.0.5 - 模块注入插件

### 外部依赖
- **OnlyOffice Document API**: 文档编辑器核心
- **Alpine.js**: 通过 CDN 引入或本地安装

## 📄 许可证

本项目采用 MIT 许可证 - 详见 LICENSE 文件

## 🤝 贡献者

感谢所有为 SmartClause 项目做出贡献的开发者！

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看 [常见问题](#-故障排除) 部分
2. 提交 [Issue](../../issues) 报告问题
3. 发起 [Pull Request](../../pulls) 贡献代码

## 📝 更新日志

### v1.0.0 (当前版本)

#### ✅ 已完成功能
- **核心组件**：完整的 OnlyOfficeManager 和 RiskItemsManager 组件
- **响应式界面**：基于 Alpine.js 3.14+ 的响应式风险项管理界面
- **分类展示**：支持按风险项类型（缺失条款/风险提示）分类展示和筛选
- **事件系统**：完善的事件系统和回调机制，支持双向通信
- **类型安全**：完整的 TypeScript 类型定义和错误处理
- **浏览器兼容**：支持 IE11 等旧浏览器（通过 Vite Legacy 插件）
- **备注管理**：完整的备注增删改查和跳转定位功能
- **性能优化**：防抖节流优化和内存管理
- **通信支持**：跨窗口/iframe 通信支持（AppMessage）

#### 🔧 技术特性
- **构建工具**：Vite 6.x + TypeScript 5.8+
- **UI 框架**：Alpine.js 3.14+ 响应式数据绑定
- **编辑器集成**：OnlyOffice Document API 深度集成
- **模块化设计**：ES6 模块化，支持按需引入
- **开发体验**：完整的类型提示和错误处理

### 🗺️ 开发路线图

#### v1.1.0 计划 (下一版本)
- 🔄 虚拟滚动支持（处理大量风险项 >1000）
- 🔄 拖拽排序功能
- 🔄 更多编辑器事件支持
- 🔄 国际化支持 (i18n)
- 🔄 主题定制功能

#### v1.2.0 计划
- 🔄 离线模式支持
- 🔄 插件化架构
- 🔄 自定义字段扩展
- 🔄 批量操作功能

#### 长期规划 (v2.0+)
- 📋 多文档并行编辑
- 📋 实时协作增强
- 📋 AI 辅助风险识别
- 📋 移动端适配优化
- 📋 微前端架构支持